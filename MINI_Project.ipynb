pip install pandas seaborn matplotlib numpy scikit-learn imbalanced-learn

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np

data = pd.read_csv("/Users/<USER>/Documents/VIT/Sem 11/Mini Proj/US healthcare data.csv")
data


data.shape

data.info()


data.describe()

missing_values = data.isnull().sum()
print("\nMissing Values:")
print(missing_values[missing_values > 0])

indices_to_drop = set()


indices_to_drop.update(list(data[data.diag_1.isna() & data.diag_2.isna() & data.diag_3.isna()].index)) #check missing values


from sklearn.pipeline import make_pipeline
from sklearn.model_selection import train_test_split



def transform_label(y):
    # Assuming 'readmitted' column contains 'Yes' and 'No' or similar categorical values
    return y.map({'Yes': 1, 'No': 0})


X = data.drop('readmitted', axis=1)
y = transform_label(data['readmitted'])

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)


X_train.head()


columns_to_drop = ['admission_type_id', 'discharge_disposition_id', 'admission_source_id',
                   'diag_2', 'diag_3', 'count_No', 'count_Steady', 'count_Down', 'count_Up']


columns_to_reduce = ['discharge_disposition_id', 'admission_source_id']

ordinal_mappings =  {
    'age': {
        '[0-10)': 0, '[10-20)': 1, '[20-30)': 2, '[30-40)': 3, '[40-50)': 4,
        '[50-60)': 5, '[60-70)': 6, '[70-80)': 7, '[80-90)': 8, '[90-100)': 9
    }}

visits_cols = ['number_emergency', 'number_outpatient', 'number_inpatient']


medicaments_cols = [
    'metformin', 'repaglinide', 'nateglinide', 'chlorpropamide','glimepiride',
    'acetohexamide', 'glipizide', 'glyburide', 'tolbutamide', 'pioglitazone',
    'rosiglitazone', 'acarbose', 'miglitol', 'troglitazone', 'tolazamide', 'examide',
    'citoglipton', 'insulin','glyburide-metformin', 'glipizide-metformin',
    'glimepiride-pioglitazone', 'metformin-rosiglitazone', 'metformin-pioglitazone'
]

diagnoses_cols = ['diag_1', 'diag_2', 'diag_3']


# Define categorical and numerical features
categorical_features = X_train.select_dtypes(include=object).columns.to_list()
numerical_features = X_train.select_dtypes(exclude=object).columns.to_list()

# Assuming medicaments_cols is defined elsewhere in your code
except_medicaments_cols = list(set(X_train.columns) - set(medicaments_cols))

# Output the features for verification
categorical_features, numerical_features, except_medicaments_cols

data.replace("?",np.nan,inplace=True)


data.isnull().sum()

print('Number of columns:',len(data.columns))


data[list(data.columns)[:10]].head()


data[list(data.columns)[10:20]].head()


data[list(data.columns)[20:30]].head()  #subset of 21-30 columns


data[list(data.columns)[30:40]].head()


data.isnull().sum().sort_values(ascending = False).head(8)
#top 8 columns with missing values

percent_missing = data.isnull().sum() * 100 / len(data)
percent_missing.sort_values(ascending=False).head(10)


data['age'].value_counts()


sns.countplot(x="age", data = data)
#plt.xticks(rotation = 90)
plt.show()

sns.countplot(data['admission_type_id'])
plt.show()

data['admission_type_id']=data['admission_type_id'].replace({1:'Emergency',2:'Emergency',7:'Emergency',
                                 5:'Not Available', 6:'Not Available', 8:'Not Available',
                                 3:'Elective',4:'Newborn'})
sns.countplot(data['admission_type_id'])
plt.show()

# Adjust this list based on available columns in your dataset
columns_to_select = ['age', 'admission_type_id']  # Add any other relevant columns here

# Filter the dataset
newborn_admissions = data[data['admission_type_id'] == 'Newborn'][columns_to_select]

# Display the filtered data
print(newborn_admissions)


data=data[data['admission_type_id']!='Newborn']


data['admission_type_id'].value_counts()


data['discharge_disposition_id'].unique()


data['discharge_disposition_id']=data['discharge_disposition_id'].replace({1:'Discharged to home',
                                        2:'Transferred to another medical facility',
                                        3:'Transferred to another medical facility',
                                        4:'Transferred to another medical facility',
                                        5:'Transferred to another medical facility',
                                        16:'Transferred to another medical facility',
                                        22:'Transferred to another medical facility',
                                        23:'Transferred to another medical facility',
                                        24:'Transferred to another medical facility',
                                        27:'Transferred to another medical facility',
                                        28:'Transferred to another medical facility',
                                        29:'Transferred to another medical facility',
                                        6:'Discharged to home with home health service',
                                        8:'Discharged to home with home health service',
                                        10:'Neonate discharge',
                                        7:'Left AMA',
                                        9:'Still patient/referred to this institution',
                                        12:'Still patient/referred to this institution',
                                        15:'Still patient/referred to this institution',
                                        17:'Still patient/referred to this institution',
                                        11:'Expired',
                                        19:'Expired',
                                        20:'Expired',
                                        21:'Expired',
                                        13:'Hospice',
                                        14:'Hospice',
                                        18:'Not Available',
                                        25:'Not Available',
                                        26:'Not Available'})


data.discharge_disposition_id.value_counts()


data=data[data['discharge_disposition_id']!='Expired']
data=data[data['discharge_disposition_id']!='Neonate discharge']
data=data[data['discharge_disposition_id']!='Hospice']

data.discharge_disposition_id.value_counts()


data['admission_source_id']=data['admission_source_id'].replace({1:'Referral',
                                   2:'Referral',
                                   3:'Referral',
                                   4:'Transferred from another health care facility',
                                   5:'Transferred from another health care facility',
                                   6:'Transferred from another health care facility',
                                   7:'Emergency',
                                   10:'Transferred from another health care facility',
                                   18:'Transferred from another health care facility',
                                   19:'Transferred from another health care facility',
                                   25:'Transferred from another health care facility',
                                   26:'Transferred from another health care facility',
                                   8 :'Emergency',
                                   9:'Not Available',
                                   15:'Not Available',
                                   17:'Not Available',
                                   20:'Not Available',
                                   21:'Not Available',
                                   11:'Delivery',
                                   12:'Delivery',
                                   13:'Delivery',
                                   14:'Delivery',
                                   23:'Delivery',
                                   22:"Transferred from another health care facility",
                                   24:'Delivery'})

data['admission_source_id'].value_counts()


data=data[data['admission_source_id']!='Delivery']


data['admission_source_id'].value_counts()


print('The number of lab tests a patient has undergone can range from',data.num_lab_procedures.min(),'to',data.num_lab_procedures.max())


data.num_procedures.value_counts()


print('The number of medications range from a minimum of',data.num_medications.min(),'to maximum of',data.num_medications.max())


data['service_utilization'] = data['number_outpatient'] + data['number_emergency'] + data['number_inpatient'] #year_visits


data.drop(['number_outpatient','number_emergency','number_inpatient'],axis=1,inplace=True)


data.head()


def map_diagnosis(data, cols):      #categorizes diagnosis codes into broader medical categories based on their numeric ranges
    for col in cols:
        data.loc[(data[col].str.contains("V")) | (data[col].str.contains("E")), col] = -1
        data[col] = data[col].astype(np.float16)

    for col in cols:
        data["temp_diag"] = np.nan
        data.loc[(data[col]>=390) & (data[col]<=459) | (data[col]==785), "temp_diag"] = "Circulatory"
        data.loc[(data[col]>=460) & (data[col]<=519) | (data[col]==786), "temp_diag"] = "Respiratory"
        data.loc[(data[col]>=520) & (data[col]<=579) | (data[col]==787), "temp_diag"] = "Digestive"
        data.loc[(data[col]>=250) & (data[col]<251), "temp_diag"] = "Diabetes"
        data.loc[(data[col]>=800) & (data[col]<=999), "temp_diag"] = "Injury"
        data.loc[(data[col]>=710) & (data[col]<=739), "temp_diag"] = "Muscoloskeletal"
        data.loc[(data[col]>=580) & (data[col]<=629) | (data[col] == 788), "temp_diag"] = "Genitourinary"
        data.loc[(data[col]>=140) & (data[col]<=239), "temp_diag"] = "Neoplasms"

        data["temp_diag"] = data["temp_diag"].fillna("Other")
        data[col] = data["temp_diag"]
        data = data.drop("temp_diag", axis=1)

    return data


df=data.copy()


def plot_diags(col,data):
    sns.countplot(x = col, data = data,
            order = data[f"{col}"].value_counts().index)
    plt.xticks(rotation = 90)
    plt.title(col)
    plt.show()

diag_cols = ["diag_1","diag_2","diag_3"]

for diag in diag_cols:
    plot_diags(diag,df)

df["diag_1"] = df["diag_1"].replace({"Circulatory":0,
                                     "Respiratory":1,
                                     "Digestive":2,
                                     "Diabetes":3,
                                     "Injury":4,
                                     "Muscoloskeletal":5,
                                     "Genitourinary":6,
                                     "Neoplasms":7,
                                    "Other":8})
df.diag_1.value_counts()


df["diag_2"] = df["diag_2"].replace({"Circulatory":0,
                                     "Respiratory":1,
                                     "Digestive":2,
                                     "Diabetes":3,
                                     "Injury":4,
                                     "Muscoloskeletal":5,
                                     "Genitourinary":6,
                                     "Neoplasms":7,
                                    "Other":8})
df.diag_2.value_counts()

df["diag_3"] = df["diag_3"].replace({"Circulatory":0,
                                     "Respiratory":1,
                                     "Digestive":2,
                                     "Diabetes":3,
                                     "Injury":4,
                                     "Muscoloskeletal":5,
                                     "Genitourinary":6,
                                     "Neoplasms":7,
                                    "Other":8})
df.diag_3.value_counts()

def plot_diags(col,data):
    sns.countplot(x = col, data = data,
            order = data[f"{col}"].value_counts().index)
    plt.xticks(rotation = 90)
    plt.title(col)
    plt.show()

diag_cols = ["diag_1","diag_2","diag_3"]

for diag in diag_cols:
    plot_diags(diag,df)

df['number_diagnoses'].value_counts()


df.max_glu_serum.value_counts()


df["max_glu_serum"] = df["max_glu_serum"].replace({">200":2,
                                                        ">300":2,
                                                        "Norm":1,
                                                        "None":0})

df.max_glu_serum.value_counts()


df.A1Cresult.value_counts()


df.columns


data.hist(bins=15, figsize=(15, 10), color='skyblue')
plt.suptitle('Distribution of Numerical Variables')
plt.show()

sns.countplot(x='readmitted', data=data, palette='viridis')
plt.title('Count of Readmission Status')
plt.show()

#numerical data for correlation matrix
numerical_data = data.select_dtypes(include=['int64', 'float64'])

# Generate and plot the correlation matrix among numerical features
numerical_data = data.select_dtypes(include=['int64', 'float64'])

plt.figure(figsize=(12, 8))
sns.heatmap(numerical_data.corr(), annot=True, cmap='coolwarm')
plt.title('Numerical Features Correlation Matrix')
plt.show()



#relationship between the target variable and other key variables
plt.figure(figsize=(10, 6))
sns.boxplot(x='readmitted', y='age', data=data, palette='viridis')
plt.title('Age Distribution by Readmission Status')
plt.show()


# List categorical columns to identify possible choices for analysis
categorical_columns = data.select_dtypes(include='object').columns
print("Categorical Columns:", categorical_columns)


plt.figure(figsize=(10, 6))
sns.countplot(x='admission_type_id', hue='readmitted', data=data, palette='viridis')
plt.title('Admission Type Distribution by Readmission Status')
plt.show()

plt.figure(figsize=(14, 6))
sns.countplot(x='discharge_disposition_id', hue='readmitted', data=data, palette='viridis')
plt.title('Discharge Disposition by Readmission Status')
plt.xticks(rotation=45)
plt.show()

df['age'] = df['age'].map({'[70-80)': 75,
                           '[60-70)': 65,
                           '[80-90)': 85,
                           '[50-60)': 55,
                           '[40-50)':45,
                           '[30-40)': 35,
                           '[90-100)':95,
                           '[20-30)':25,
                           '[10-20)':15,
                           '[0-10)': 5})
df['age'].value_counts()


data.groupby('age').size().plot(kind='bar')
plt.ylabel('Count')
plt.show()

# Loop through categorical columns to check association with 'readmitted' status
categorical_columns = data.select_dtypes(include='object').columns
chi_square_results = {}


import pandas as pd
from scipy.stats import chi2_contingency


for col in categorical_columns:
    contingency_table = pd.crosstab(data[col], data['readmitted'])
    chi2, p, dof, expected = chi2_contingency(contingency_table)
    chi_square_results[col] = (chi2, p)

chi_square_df = pd.DataFrame(chi_square_results, index=['Chi-square Statistic', 'p-value']).T
print(chi_square_df)

import seaborn as sns
import matplotlib.pyplot as plt


plt.figure(figsize=(10, 6))
sns.boxplot(x='readmitted', y='time_in_hospital', data=data)
plt.title('Time in Hospital Distribution by Readmission Status')
plt.show()

df.change.value_counts()


df['change'] = df['change'].replace({'No' : 0, 'Ch' : 1})


df.diabetesMed.value_counts()


df['diabetesMed'] = df['diabetesMed'].replace({'Yes' : 1, 'No' : 0})


df.readmitted.value_counts()


df['readmitted'] = df['readmitted'].replace('>30', 'NO')
df['readmitted']= df['readmitted'].replace( {'NO': 0, '<30': 1} ).astype(int)

df.shape


df['admission_source_id']=df['admission_source_id'].replace({'Emergency':0,'Referral':1,
                                                             'Not Available':2,'Transferred from another health care facility':3})

df['discharge_disposition_id']=df['discharge_disposition_id'].replace({'Discharged to home':0,'Transferred to another medical facility':1,
                                                                       'Not Available':2,'Left AMA':3,'Discharged to home with home health service':5,
                                                                       'Still patient/referred to this institution':4})


df['admission_type_id']=df['admission_type_id'].replace({'Emergency':0,'Elective':1,'Not Available':2})


df['A1Cresult']=df['A1Cresult'].replace({'None' :0,'>7':2, '>8':2, 'Norm':1})


for c in list(df.columns):
    #  get a list of unique values
    n=df[c].unique()
    # if the number of unique values is less than 30 print values ,otherwise print the number of unoque values
    if len(n)<30:
        print(c)
        print(n)
    else:
        print(c+':'+str(len(n))+'unique values')

df.columns

X_num = X.select_dtypes(include=['float64', 'int64'])  # Numerical columns
X_cat = X.select_dtypes(include=['object'])  # Categorical columns


import pandas as pd
from sklearn.preprocessing import LabelEncoder


# Initialize LabelEncoder
label_encoder = LabelEncoder()

# Encode binary categorical variables using Label Encoding
binary_columns = ['change', 'diabetesMed']
for col in binary_columns:
    df[col] = label_encoder.fit_transform(df[col])

# One-Hot Encode remaining categorical variables
one_hot_columns = ['age', 'max_glu_serum', 'A1Cresult', 'metformin', 'repaglinide', 'glipizide', 'insulin']
df = pd.get_dummies(df, columns=one_hot_columns, drop_first=True)

# Check encoding results
print(df.head())


# Convert boolean columns to integers (0 and 1)
bool_columns = ['metformin_Up', 'repaglinide_No', 'repaglinide_Steady', 'repaglinide_Up',
                'glipizide_No', 'glipizide_Steady', 'glipizide_Up', 
                'insulin_No', 'insulin_Steady', 'insulin_Up']
df[bool_columns] = df[bool_columns].astype(int)

# Encode remaining categorical columns with One-Hot Encoding
# Define columns that need one-hot encoding
categorical_columns = ['admission_type_id', 'discharge_disposition_id', 'admission_source_id', 
                       'diag_1', 'diag_2', 'diag_3']
df = pd.get_dummies(df, columns=categorical_columns, drop_first=True)

# Check the transformed DataFrame
print(df.head())


X = df.drop(columns=['readmitted'])  # drop target column from features
y = df['readmitted']  # target variable


from sklearn.model_selection import train_test_split

# Perform train-test split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)


from imblearn.over_sampling import SMOTE
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report
from collections import Counter 

# Initialize SMOTE
smote = SMOTE(random_state=42)

# Fit and resample the training data
X_train_resampled, y_train_resampled = smote.fit_resample(X_train, y_train)

# Check the class distribution after resampling
print(f'Original dataset shape: {Counter(y_train)}')
print(f'Resampled dataset shape: {Counter(y_train_resampled)}')


model = RandomForestClassifier(random_state=42)
model.fit(X_train_resampled, y_train_resampled)

# Predictions
y_pred = model.predict(X_test)

# Evaluate model performance
accuracy = accuracy_score(y_test, y_pred)
report = classification_report(y_test, y_pred)

print(f'Accuracy: {accuracy}')
print(f'Classification Report:\n{report}')

from sklearn.model_selection import train_test_split

# Assuming you have already applied SMOTE and have X_resampled and y_resampled
X_train, X_test, y_train, y_test = train_test_split(X_train_resampled, y_train_resampled, random_state=42, test_size=0.25)

print('Shape of X_train: ', X_train.shape)
print('Shape of X_test: ', X_test.shape)
print('Shape of y_train: ', y_train.shape)
print('Shape of y_test: ', y_test.shape)


from sklearn.metrics import accuracy_score, f1_score,precision_score,recall_score, classification_report

results = {'Classification Model':[],'Accuracy':[], 'F1 Score':[]}

from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, classification_report

# Initialize the Random Forest classifier
clf = RandomForestClassifier()

# Fit the model on the training data
clf.fit(X_train, y_train)

# Make predictions on the test set
y_pred = clf.predict(X_test)

# Print classification model name
print('Classification Model: {}'.format(clf.__class__.__name__))

# Print evaluation metrics using the built-in round function
print('Accuracy Score: ', round(accuracy_score(y_test, y_pred), 4))
print('F1 Score: ', round(f1_score(y_test, y_pred, average='binary'), 4))
print('Precision Score: ', round(precision_score(y_test, y_pred), 4))
print('Recall Score: ', round(recall_score(y_test, y_pred, average='binary'), 4))

# Print the classification report
print('Classification Report: \n', classification_report(y_test, y_pred))

# Update results dictionary
if clf.__class__.__name__ not in results['Classification Model']:
    results['Classification Model'].append(clf.__class__.__name__)
    results['Accuracy'].append(round(accuracy_score(y_test, y_pred), 4))
    results['F1 Score'].append(round(f1_score(y_test, y_pred, average='binary'), 4))


from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, classification_report

# Initialize the results dictionary
results = {
    'Classification Model': [],
    'Accuracy': [],
    'F1 Score': [],
}

clf = LogisticRegression()
clf.fit(X_train, y_train)

y_pred = clf.predict(X_test)

print('Classification Model: {}'.format(clf.__class__.__name__))
print('Accuracy Score: ', round(accuracy_score(y_test, y_pred), 4))
print('F1 Score: ', round(f1_score(y_test, y_pred, average='binary'), 4))
print('Precision Score: ', round(precision_score(y_test, y_pred), 4))
print('Recall Score: ', round(recall_score(y_test, y_pred, average='binary'), 4))
print('Classification Report: \n', classification_report(y_test, y_pred))


if clf.__class__.__name__ not in results['Classification Model']:
    results['Classification Model'].append(clf.__class__.__name__)
    results['Accuracy'].append(round(accuracy_score(y_test, y_pred), 4))
    results['F1 Score'].append(round(f1_score(y_test, y_pred, average='binary'), 4))


from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score
# Finding the optimum k value
values = []
neighbors = np.arange(1, 10)

for k in neighbors:
    clf = KNeighborsClassifier(n_neighbors=k, metric='minkowski')
    clf.fit(X_train, y_train)
    y_pred = clf.predict(X_test)
    score = accuracy_score(y_test, y_pred)
    values.append(score)

# Plotting k vs accuracy_score to get optimum k
plt.figure(figsize=(8, 5))
plt.plot(neighbors, values, 'o-', color='blue', markersize=8)
plt.title('Accuracy Score vs. k (kNN)')
plt.xticks(np.arange(1, 10, 1.0))
plt.xlabel('Number of Neighbors (k)')
plt.ylabel('Accuracy Score')
plt.grid()
plt.show()

from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, f1_score, classification_report

# Initialize the kNN classifier with k=2
clf = KNeighborsClassifier(n_neighbors=2, metric='minkowski')

# Fit the model on the training data
clf.fit(X_train, y_train)

# Make predictions on the test set
y_pred = clf.predict(X_test)

# Print classification model name
print('Classification Model: {}'.format(clf.__class__.__name__))

# Print evaluation metrics using the built-in round function
print('Accuracy Score: ', round(accuracy_score(y_test, y_pred), 4))
print('F1 Score: ', round(f1_score(y_test, y_pred, average='macro'), 4))
print('Classification Report: \n', classification_report(y_test, y_pred))

# Update results dictionary
if clf.__class__.__name__ not in results['Classification Model']:
    results['Classification Model'].append(clf.__class__.__name__)
    results['Accuracy'].append(round(accuracy_score(y_test, y_pred), 4))
    results['F1 Score'].append(round(f1_score(y_test, y_pred, average='macro'), 4))


from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, classification_report

# Initialize the Decision Tree classifier
clf = DecisionTreeClassifier()

# Fit the model on the training data
clf.fit(X_train, y_train)

# Make predictions on the test set
y_pred = clf.predict(X_test)

# Print classification model name
print('Classification Model: {}'.format(clf.__class__.__name__))

# Print evaluation metrics using the built-in round function
print('Accuracy Score: ', round(accuracy_score(y_test, y_pred), 4))
print('F1 Score: ', round(f1_score(y_test, y_pred, average='binary'), 4))
print('Precision Score: ', round(precision_score(y_test, y_pred), 4))
print('Recall Score: ', round(recall_score(y_test, y_pred, average='binary'), 4))

# Print the classification report
print('Classification Report: \n', classification_report(y_test, y_pred))

# Update results dictionary
if clf.__class__.__name__ not in results['Classification Model']:
    results['Classification Model'].append(clf.__class__.__name__)
    results['Accuracy'].append(round(accuracy_score(y_test, y_pred), 4))
    results['F1 Score'].append(round(f1_score(y_test, y_pred, average='binary'), 4))


import numpy as np
import pandas as pd
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

# Sample Dataset (Mock data for demonstration)
# Replace this with your actual dataset
data = {
    'age': [50, 60, 45, 70, 55],
    'admission_type_id': [1, 2, 1, 3, 2],
    'discharge_disposition_id': [1, 1, 2, 3, 2],
    'time_in_hospital': [5, 10, 3, 8, 7],
    'num_lab_procedures': [45, 70, 30, 80, 65],
    'num_procedures': [2, 3, 1, 0, 4],
    'num_medications': [10, 20, 15, 25, 18],
    'number_diagnoses': [6, 8, 5, 9, 7],
    'diabetesMed': [1, 0, 1, 0, 1],  # Binary feature for diabetes medication
    'readmitted': [0, 1, 0, 1, 0]    # Target variable
}

# Convert the dictionary to a DataFrame
df = pd.DataFrame(data)

# Features and target variable
X = df.drop(columns=['readmitted'])
y = df['readmitted']

# Split the data (optional for this use case, just to simulate training)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Standardize the data
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)

# Train Logistic Regression
model = LogisticRegression()
model.fit(X_train_scaled, y_train)

# Example single data entry for prediction
single_data_entry = pd.DataFrame([{
    'age': 52,
    'admission_type_id': 1,
    'discharge_disposition_id': 1,
    'time_in_hospital': 6,
    'num_lab_procedures': 50,
    'num_procedures': 1,
    'num_medications': 12,
    'number_diagnoses': 5,
    'diabetesMed': 1
}])

# Preprocess the input data (same scaling as training)
single_data_scaled = scaler.transform(single_data_entry)

# Make a prediction
prediction = model.predict(single_data_scaled)
prediction_proba = model.predict_proba(single_data_scaled)

# Output the prediction
print(f"Prediction (Readmitted = 1, Not Readmitted = 0): {prediction[0]}")
print(f"Prediction Probabilities: {prediction_proba[0]}")
